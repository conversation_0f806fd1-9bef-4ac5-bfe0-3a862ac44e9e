# Inputs for the Training Pipeline design

## Notes from <PERSON><PERSON>


```letter
<PERSON> / <PERSON>,

 

Here are my notes / comments from our feedback loop meeting earlier today that I want to make sure we capture.  These are in no particular order.   

 

Feedback loop needs to address 3 areas: classification, splitting, and data capture
Models should be trained using the previous model and the new training sample set.  Historical training sample sets should not be needed and may not be available.
<PERSON> will look at what language we may be able to add to existing / future agreements to allow us to retain a small percentage of production  files and metadata in order to produce a historical dataset for training
We need to build out a measurement process to evaluate how accurate our models are by comparing the system output to the human output… (Power BI reports )
<PERSON> and <PERSON> will work together to build out a work breakdown and identify individual tasks required to implement the feedback loop.
 

 

Team,  please add to this thread for any other items we talked about or any other ideas you may have as you work on this project.

 

--Matthew
```

## General Requiriments

- Currently the aa_record_ranger_ml_pipeline does not have a proper training pieca that can generate new versions of the models in the code.
   - For reference there is information ar [Models Information folder](../../Migration/Model/Mistral_Nemo_Instruct/)
- Check this document that has Description of the current state of the ML Pipeline (without training) and with a initial proposal for a automated training component at [Record Ranger ML Pipeline Documentation](Record_Ranger_ML_Pipeline_Documentation.md)
- Considerations:
   - Business conditions: 
     - For purposes of the training element clients will hand over document samples for their document types ocr and data capture requirements, this documents should be considered to be kept in a temporary manner. Due to HIPAA compliance regulations because the documents processed in record ranger are related to Personal Health information ( Prescriptions, ), personal health information that links to a person cannot be stored.
     - In order to improve the models having a feedback loop or with a Human reinforcement by using data of the QA_TOOL could be used but considering that no raw data ( original image + json labels) can be stored, explore the possibility to use that data to train a new version of the models but without storing "production" information, only doing incremental training or if possible use transfer learning.
     - For tracking the training data when used there should be a tracking of the document types, labels of information in the image and manner to track the original client that provided the data in order to do proper data training management in the case that data has to be deleted because of compliance issues.
     - The trainign pipeline should consider as proposal two types of model training, one where all training data can be kept in order to create the new versions, and other, the most likely to be used to incrementally improve the models only with new data that gets deleted over time due to clients and HIPAA compliance.
     - Other consideration is a potential de-identifier piece that is able to generate training data for the company and product by removing only filled and personal information eg; Names, dates, prescriptionsm etc and keeping the original "template" of the document in order to generate HIPAA compliant and client-free data to store to be potentially used as sub set for the training dataset.
     - The automated or semiautomated model training pipeline must consider, a) using implementation initial data samples from clientes when they request record ranger to support additional document types and metadata this is data than can be kept temporalily in order to generate initial implementation models and be erased after 90 days of model development, b) the use of the QA_TOOL data to do incremental training of the models, especially data that is misread by the models and requires human intervention this can be detected by comparing initial metadata extractions with the final human validated metadata, c) the use of a de-identifier piece to generate HIPAA compliant and client-free data to store to be potentially used as sub set for the training dataset.
     - The initial training cycle is considered to be monthly delivered saving older versions of models saved as baseline in the case the A/B testing incremental deployment of the new models does not work as expected, based on a specific metric threshold.
     - design a set of metrics to make informed decisions on the performance of current and new models.
     - Add to the current implementation database tracking data for the json metadata after the current automated process and the json obtained when passed to the qa_tool in order to generate the training dataset.

