module "ecr" {
  providers = {
    aws = aws.us-east-2
  }
  source   = "../../local-modules/ecr"
  for_each = toset(var.ecrs)
  names = [
    each.key
  ]

  lifecycle_policy  = local.ecr_lifecycle_policy
  repository_policy = local.ecr_repository_policy

}


locals {
  ecr_lifecycle_policy  = <<EOF
{
  "rules": [
    {
      "rulePriority": 1,
      "description": "Keep last 100 images",
      "selection": {
        "tagStatus": "any",
        "countType": "imageCountMoreThan",
        "countNumber": 100
      },
      "action": {
        "type": "expire"
      }
    }
  ]
}
EOF
  ecr_repository_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "accounts",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "arn:aws:iam::************:root",
          "arn:aws:iam::************:root",
          "arn:aws:iam::************:root",
          "arn:aws:iam::************:root",
          "arn:aws:iam::************:root"
        ]
      },
      "Action": [
        "ecr:BatchCheckLayerAvailability",
        "ecr:BatchDeleteImage",
        "ecr:BatchGetImage",
        "ecr:CompleteLayerUpload",
        "ecr:DeleteLifecyclePolicy",
        "ecr:DeleteRepository",
        "ecr:DeleteRepositoryPolicy",
        "ecr:DescribeImages",
        "ecr:DescribeRepositories",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetLifecyclePolicy",
        "ecr:GetLifecyclePolicyPreview",
        "ecr:GetRepositoryPolicy",
        "ecr:InitiateLayerUpload",
        "ecr:ListImages",
        "ecr:PutImage",
        "ecr:PutLifecyclePolicy",
        "ecr:UploadLayerPart"
      ]
    }
  ]
}
EOF
}
