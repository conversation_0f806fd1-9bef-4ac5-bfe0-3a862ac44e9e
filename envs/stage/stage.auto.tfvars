 environment                   = "stage"
 tenant                        = "atom-advantage"
 domain                        = "atomadvantage.ai"
 account_id                    = "************"
 sns_endpoint                  = ["<EMAIL>"]

 sso_admin_role     = "27e9c75c3fc27e8e"

 enable_cf                     = true
 enable_nat_gateway            = true

 tags = {
    Terraform   = "true"
    Environment = "stage"
    tenant      = "atom-advantage"
 }

##  eu-central-1
 cidr_block                    = "10.74.0.0/16"
 region                        = "us-east-2"



 security_groups = {
   "rds" = {
      "backend" = { from_port = 5432, to_port = 5432, protocol = "TCP", source = "10.74.0.0/16" }
   }
   "management" = {
      "ssh" = { from_port = 22, to_port = 22, protocol = "TCP", source = "0.0.0.0/0" },
      "rdp" = { from_port = 3389, to_port = 3389, protocol = "TCP", source = "0.0.0.0/0" }
   }
}

#APP
ssm_backend = ["API_KEY", "JWT_SECRET_KEY", "JWT_REFRESH_SECRET_KEY", "SOURCE_SECRET_KEY", "MAILGUN_API_DOMAIN", "MAILGUN_API_KEY", "MAIL_TOKEN"]


# RDS

rds_name = "backend"
rds_engine = "postgres"
rds_family = "postgres16"
rds_username = "backend"
rds_engine_version = 16
apply_immediately = true
rds_allocated_storage = 20
rds_instance_class = "db.t3.micro"
rds_port = 5432
rds_multi_az = false

powerbi_proxy_consumer_account_arn = "arn:aws:iam::************:root"

powerbi_proxy_rds_targets = {
  "ehs-backend-db" = {
    listener_port = 5432
    rds_hostname  = "backend.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
    rds_port      = 5432
  },
  "arriba-backend-db" = {
    listener_port = 5433 # Must be a unique port on the NLB
    rds_hostname  = "backend-ariba.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
    rds_port      = 5432
  }
  # Add more RDS instances here if needed
}
