provider "aws" {
  region              = var.region
  allowed_account_ids = [var.account_id]
  alias               = "us-east-2"

  assume_role {
    role_arn = "arn:aws:iam::${var.account_id}:role/OrganizationAccountAccessRole"
  }
}

provider "dns" {}

terraform {
  required_providers {
    aws = {
      source                = "hashicorp/aws"
      version               = ">= 5.55"
      configuration_aliases = [aws.us-east-2]
    }
    dns = {
      source  = "hashicorp/dns"
      version = ">= 3.0"
    }
  }
}
