---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-post-processor
  namespace: training
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-post-processor
  template:
    metadata:
      labels:
        app: qa-post-processor
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - training
      tolerations:
        - key: "training"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      # serviceAccountName: qa-post-processor
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: downloader
      dnsPolicy: ClusterFirst
      
      containers:
        - name: qa-post-processor
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa-post-processor:73d4225dd5ec679d676e5aa595729530e1c1ec26
          imagePullPolicy: Always
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          env:
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "sqMPOQPW04o3Oaagd2vb"
            - name: MINIO_SECRET_KEY
              value: "p5uCaFcPCyZY1t562oJVwUILHqTXiqSSvNBpgzgc"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-stage"
            - name: MINIO_SECURE
              value: "false"
            - name: SFTP_REMOTE_HOST
              value: "localhost"
            - name: SFTP_REMOTE_PORT
              value: "222"
            - name: SFTP_REMOTE_USER
              value: "sftp"
            - name: SFTP_REMOTE_PASSWORD
              value: "test"
            - name: SFTP_REMOTE_PATH
              value: "folder"
            - name: PROJECT_NAME
              value: "atom-advantage-packets-stage"
            - name: APP_NAME
              value: "qa-post-processor"
            - name: REMOTE_RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: REMOTE_RABBITMQ_PORT
              value: "5672"
            - name: REMOTE_RABBITMQ_USERNAME
              value: "app_user"
            - name: REMOTE_RABBITMQ_PASSWORD
              value: "app_password"
            - name: REMOTE_RABBITMQ_DEFAULT_VHOST
              value: "/"  
            - name: REMOTE_RABBITMQ_TO_BACKEND_QA_QUEUE_NAME
              value: "training_to_backend_qa"
            - name: REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME
              value: "training_from_backend_qa"
            - name: REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME
              value: "training_packet_status"
            - name: REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT_QUEUE_NAME
              value: "training_from_backend_screenshot"
            - name: RABBITMQ_TO_CLASSIFY_QUEUE_NAME
              value: "training_to_classify"
            - name: RABBITMQ_TO_SPLIT_QUEUE_NAME
              value: "training_to_split"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "training_to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "training_to_metadata_postprocess"
            - name: RABBITMQ_TO_VALIDATE_QUEUE_NAME
              value: "training_to_validate"
            - name: RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME
              value: "training_to_qa_postprocess"
            - name: RABBITMQ_TO_UPLOAD_QUEUE_NAME
              value: "training_to_upload"
            - name: RABBITMQ_TO_SCREENSHOT_QUEUE_NAME
              value: "training_to_screenshot"
            - name: RABBITMQ_TO_SCREENSHOT_POSTPROCESS_QUEUE_NAME
              value: "training_to_screenshot_postprocess"
            - name: RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "app_user"
            - name: RABBITMQ_PASSWORD
              value: "app_password"
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: API_HOST
              value: "qa-backend.training.svc.cluster.local"
            - name: API_PORT
              value: "80"
            - name: SERVICE_BUS_CONNECTION_STRING
              value: "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=kamil;SharedAccessKey=MQGjhOgipRmUEZIATcu1hFHTrUlNOFfgu+ASbKulQ10=;EntityPath=sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1"
            - name: TOPIC_NAME
              value: "splitter-stage"
            - name: SUBSCRIPTION_NAME
              value: "08bcb323-cbf9-4ab1-b6cf-12dcd17bb8ce"
            - name: SERVICE_BUS_QUEUE_NAME
              value: "sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1"
            - name: S3_BUCKET_NAME
              value: "atom-advantage-packets-stage"
            - name: S3_REGION
              value: "us-east-2"
            - name: S3_ENDPOINT_URL
              value: "https://atom-advantage-packets-stage.s3.us-east-2.amazonaws.com"
            - name: PGSQL_HOST_REMOTE
              value: "backend-training.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT_REMOTE
              value: "5432"
            - name: PGSQL_USERNAME_REMOTE
              value: "backend"
            - name: PGSQL_PASSWORD_REMOTE
              value: "uT9ASw3d8YSRdxxN"
            - name: PGSQL_DB_NAME_REMOTE
              value: "queue_training_stage"
            - name: PGSQL_SSL_MODE_REMOTE
              value: "require"
            - name: PGSQL_HOST
              value: "backend-training.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "uT9ASw3d8YSRdxxN"
            - name: PGSQL_DB_NAME
              value: "rr_training_stage"
            - name: MONITOR_HOST
              value: "http://monitor.apps.svc.cluster.local"
            - name: MONITOR_PORT
              value: "7795"
            - name: CHANNEL
              value: "servicebus_queue"
            - name: ADD_VALIDATION_REPORT_DATA
              value: "no"
            - name: AZURE_STORAGE_CONTAINER_NAME
              value: "splitter-stage"
            - name: AZURE_STORAGE_CONNECTION_STRING
              value: "DefaultEndpointsProtocol=https;AccountName=stageatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
            - name: TENANT_NAME
              value: "training"
            - name: CRYPTOGRAPHY_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="