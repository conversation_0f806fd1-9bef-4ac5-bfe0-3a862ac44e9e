---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-frontend
  namespace: ariba
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-frontend
  template:
    metadata:
      labels:
        app: qa-frontend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - ariba
      tolerations:
        - key: "ariba"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      dnsPolicy: ClusterFirst
      containers:
        - name: qa-frontend
          image: 112623991000.dkr.ecr.us-east-2.amazonaws.com/qa-frontend:7bb17bdb-ariba-stage
          # readinessProbe:
          #   httpGet:
          #     path: /health
          #     port: 3000
          #   initialDelaySeconds: 30
          #   periodSeconds: 15
          #   timeoutSeconds: 5 
          # livenessProbe:
          #   httpGet:
          #     path: /health  
          #     port: 3000
          #   initialDelaySeconds: 30 
          #   periodSeconds: 15
          #   timeoutSeconds: 5
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
