import axios, { AxiosResponse } from "axios";

const API_URL = import.meta.env.VITE_API_URL + "/api/v1";

async function submitAll(data?: SubmitType): Promise<SubmitType> {
  const token = JSON.parse(localStorage.getItem("userAuth")).token;

  const packet_id = localStorage.getItem("activeFile");
  const dataToSend = data || {};

  const url = `/chunk/submit/${packet_id}`;

  try {
    const res: AxiosResponse<any, any> = await axios.put(
      API_URL + url,
      dataToSend,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "x-api-key": import.meta.env.VITE_API_KEY,
        },
      },
    );

    return res.data;
  } catch (e) {
    console.error(e.response);
    return null;
  }
}

async function partialUpdate(data?: SubmitType): Promise<any> {
  const token = JSON.parse(localStorage.getItem("userAuth")).token;
  const packet_id = localStorage.getItem("activeFile");
  const dataToSend = data || {};
  if (!packet_id) {
    console.log("No current doc");
    return;
  }
  try {
    const res: AxiosResponse<any, any> = await axios.put(
      API_URL + `/chunk/part_update/${packet_id}`,
      dataToSend,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    return res.data;
  } catch (e) {
    console.error(e.response);
    return null;
  }
}

async function delegate(data: SubmitType): Promise<any> {
  const token = JSON.parse(localStorage.getItem("userAuth")).token;
  const packet_id = localStorage.getItem("activeFile");
  const dataToSend = data || {};

  try {
    const res: AxiosResponse<any, any> = await axios.put(
      API_URL + `/chunk/delegate/${packet_id}`,
      dataToSend,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
          "x-api-key": import.meta.env.VITE_API_KEY,
        },
      },
    );

    localStorage.setItem("activeFile", res.data.packet_id);
    window.dispatchEvent(new Event("storage"));
    return res.data;
  } catch (e) {
    console.error(e.response);
    return null;
  }
}

async function getStatistics(): Promise<any> {
  const token = JSON.parse(localStorage.getItem("userAuth")).token;

  try {
    const res: AxiosResponse<any, any> = await axios.get(
      API_URL + `/statistic `,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    return res.data;
  } catch (e) {
    console.error(e.response);
    return null;
  }
}

export { delegate, getStatistics, partialUpdate, submitAll };
